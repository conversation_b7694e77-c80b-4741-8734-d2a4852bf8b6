#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
闲鱼详情页数据处理器 - 精简版（影刀专用）
作者: AI Assistant
功能: 处理详情页数据并更新Excel文件中对应的商品信息
"""

import json
import pandas as pd
import os
import time
import shutil
import tempfile
from datetime import datetime
from typing import Dict, List
import logging

# 配置
OUTPUT_BASE_DIR = r"D:\AppData\SelfSync\Code\Python\Tool\toolProject\xianyu\download"
EXCEL_DIR = os.path.join(OUTPUT_BASE_DIR, "excel")
LOG_DIR = os.path.join(OUTPUT_BASE_DIR, "logs")

def ensure_dirs():
    """确保目录存在"""
    os.makedirs(EXCEL_DIR, exist_ok=True)
    os.makedirs(LOG_DIR, exist_ok=True)

def setup_logger(product_id="default"):
    """设置日志"""
    logger = logging.getLogger(f'detail_{product_id}')
    logger.handlers.clear()
    logger.setLevel(logging.INFO)

    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')

    # 控制台
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    # 文件
    ensure_dirs()
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_file = os.path.join(LOG_DIR, f"detail_{product_id}_{timestamp}.log")
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)

    return logger

def extract_detail_data(detail_response, logger):
    """从详情页响应中提取数据"""
    try:
        # 解析响应数据
        if isinstance(detail_response, str):
            data = json.loads(detail_response)
        else:
            data = detail_response

        # 获取body数据
        body = data.get('body', {})
        if isinstance(body, str):
            body = json.loads(body)

        # 获取cardList
        card_list = body.get('data', {}).get('cardList', [])
        if not card_list:
            logger.warning("未找到cardList")
            return {}

        detail_data = {}

        # 查找商品详情卡片
        for card in card_list:
            if card.get('cardType') == 100005:
                card_data = card.get('cardData', {})

                # 基本信息
                if card_data.get('itemId'):
                    detail_data['商品ID'] = card_data['itemId']
                if card_data.get('area'):
                    detail_data['详情地区'] = card_data['area']

                # 用户信息
                user_info = card_data.get('user', {})
                if user_info.get('userNick'):
                    detail_data['详情卖家昵称'] = user_info['userNick']

                # 发布时间
                publish_time = card_data.get('clickParam', {}).get('args', {}).get('publishTime')
                if publish_time:
                    try:
                        timestamp = int(publish_time) / 1000
                        publish_date = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
                        detail_data['详情发布时间'] = publish_date
                    except:
                        pass

                # 解析标签
                fish_tags = card_data.get('fishTags', {})
                for region_data in fish_tags.values():
                    for tag in region_data.get('tagList', []):
                        tag_data = tag.get('data', {})
                        label_id = tag_data.get('labelId')
                        content = tag_data.get('content', '')

                        if label_id and content:
                            tag_map = {
                                '9': '详情想要人数',
                                '13': '详情包邮',
                                '41': '详情原价',
                                '468': '详情关注过',
                                '919': '详情卖家信用',
                                '824': '详情回头客',
                                '598': '详情降价信息',
                                '1017': '详情可小刀'
                            }

                            if label_id in tag_map:
                                if label_id in ['13', '468', '1017']:
                                    detail_data[tag_map[label_id]] = '是'
                                else:
                                    detail_data[tag_map[label_id]] = content

                break

        logger.info(f"提取到 {len(detail_data)} 个字段")
        return detail_data

    except Exception as e:
        logger.error(f"详情数据提取失败: {e}")
        return {}

def find_excel_file(excel_path_or_name):
    """查找Excel文件"""
    if os.path.isabs(excel_path_or_name) and os.path.exists(excel_path_or_name):
        return excel_path_or_name

    if not os.path.isabs(excel_path_or_name):
        excel_path = os.path.join(EXCEL_DIR, excel_path_or_name)
        if os.path.exists(excel_path):
            return excel_path

        # 查找匹配文件
        if os.path.exists(EXCEL_DIR):
            for file in os.listdir(EXCEL_DIR):
                if file.endswith('.xlsx') and excel_path_or_name.replace('.xlsx', '') in file:
                    return os.path.join(EXCEL_DIR, file)

    return None

def update_excel_safe(excel_path, product_id, detail_data, logger):
    """安全更新Excel文件"""
    max_retries = 5

    try:
        # 读取Excel文件
        for attempt in range(max_retries):
            try:
                df = pd.read_excel(excel_path, engine='openpyxl')
                logger.info(f"读取Excel成功，共 {len(df)} 行")
                break
            except PermissionError:
                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 2
                    logger.warning(f"文件被占用，等待 {wait_time} 秒...")
                    time.sleep(wait_time)
                else:
                    logger.error("文件被占用，无法读取")
                    return False
        else:
            return False

        # 查找商品行
        product_row_index = None
        for index, row in df.iterrows():
            if str(row['商品ID']) == str(product_id):
                product_row_index = index
                break

        if product_row_index is None:
            logger.warning(f"未找到商品ID: {product_id}")
            return False

        logger.info(f"找到商品记录，行号: {product_row_index + 2}")

        # 更新数据
        updated_fields = []
        for field, value in detail_data.items():
            if field not in df.columns:
                df[field] = ''
                logger.info(f"添加新列: {field}")

            df.at[product_row_index, field] = value
            updated_fields.append(field)

        # 创建临时文件
        temp_dir = os.path.dirname(excel_path)
        temp_file = os.path.join(temp_dir, f"temp_{os.path.basename(excel_path)}")

        # 保存到临时文件
        with pd.ExcelWriter(temp_file, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='商品数据')

            # 设置列宽
            worksheet = writer.sheets['商品数据']
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width

        # 替换原文件
        for attempt in range(max_retries):
            try:
                backup_file = excel_path + ".backup"
                if os.path.exists(backup_file):
                    os.remove(backup_file)
                shutil.copy2(excel_path, backup_file)
                shutil.move(temp_file, excel_path)
                if os.path.exists(backup_file):
                    os.remove(backup_file)

                logger.info(f"更新成功，更新了 {len(updated_fields)} 个字段")
                return True

            except PermissionError:
                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 3
                    logger.warning(f"文件被占用，等待 {wait_time} 秒...")
                    time.sleep(wait_time)
                else:
                    logger.error("文件被占用，无法更新")
                    if os.path.exists(temp_file):
                        os.remove(temp_file)
                    return False

        return False

    except Exception as e:
        logger.error(f"更新Excel失败: {e}")
        return False

# ==================== 影刀专用函数 ====================

def yingdao_process_detail(detail_response, excel_path_or_name, product_id):
    """影刀专用：处理单个商品详情"""
    try:
        logger = setup_logger(product_id)
        logger.info(f"开始处理商品详情: {product_id}")

        # 查找Excel文件
        excel_path = find_excel_file(excel_path_or_name)
        if not excel_path:
            logger.error(f"未找到Excel文件: {excel_path_or_name}")
            return False

        logger.info(f"Excel文件: {excel_path}")

        # 提取详情数据
        detail_data = extract_detail_data(detail_response, logger)
        if not detail_data:
            logger.warning("详情数据解析失败")
            return False

        # 更新Excel
        success = update_excel_safe(excel_path, product_id, detail_data, logger)

        if success:
            logger.info(f"商品详情处理成功: {product_id}")
        else:
            logger.error(f"商品详情处理失败: {product_id}")

        return success

    except Exception as e:
        if 'logger' in locals():
            logger.error(f"处理异常: {e}")
        return False

def yingdao_batch_process_details(detail_list, excel_path_or_name):
    """影刀专用：批量处理详情页数据"""
    try:
        logger = setup_logger("batch")
        logger.info(f"开始批量处理，共 {len(detail_list)} 个商品")

        stats = {
            'total': len(detail_list),
            'success': 0,
            'failed': 0,
            'errors': []
        }

        for i, detail_item in enumerate(detail_list, 1):
            product_id = detail_item.get('product_id', '')
            detail_response = detail_item.get('detail_response', '')

            logger.info(f"处理第 {i}/{len(detail_list)} 个商品: {product_id}")

            try:
                success = yingdao_process_detail(detail_response, excel_path_or_name, product_id)
                if success:
                    stats['success'] += 1
                    logger.info(f"商品 {product_id} 处理成功")
                else:
                    stats['failed'] += 1
                    stats['errors'].append(f"商品 {product_id} 处理失败")
                    logger.error(f"商品 {product_id} 处理失败")
            except Exception as e:
                stats['failed'] += 1
                error_msg = f"商品 {product_id} 处理异常: {e}"
                stats['errors'].append(error_msg)
                logger.error(error_msg)

        logger.info(f"批量处理完成 - 总数: {stats['total']}, 成功: {stats['success']}, 失败: {stats['failed']}")
        return stats

    except Exception as e:
        if 'logger' in locals():
            logger.error(f"批量处理异常: {e}")
        return {'total': 0, 'success': 0, 'failed': 0, 'errors': [str(e)]}

def yingdao_get_pending_products(excel_path_or_name):
    """
    影刀专用：获取需要处理详情的商品列表

    参数:
        excel_path_or_name: Excel文件的完整路径或文件名

    返回:
        list: 需要处理详情的商品列表
    """
    try:
        # 查找Excel文件
        excel_path = find_excel_file(excel_path_or_name)
        if not excel_path:
            return []

        # 读取Excel文件
        df = pd.read_excel(excel_path, sheet_name='商品数据')

        # 查找需要详情的商品
        pending_products = []

        for index, row in df.iterrows():
            # 检查是否标记为需要详情
            need_details = row.get('需要详情', '否') == '是'
            # 检查是否已获取详情
            has_details = row.get('详情已获取', '否') == '是'

            if need_details and not has_details:
                pending_products.append({
                    'product_id': str(row['商品ID']),
                    'title': row.get('商品标题', ''),
                    'price': row.get('价格', ''),
                    'detail_url': row.get('详情链接', '')
                })

        logger.info(f"📊 找到 {len(pending_products)} 个待处理详情的商品")
        return pending_products

    except Exception as e:
        logger.error(f"❌ 获取待处理商品失败: {e}")
        return []

# ==================== 新版详情处理函数 ====================

def setup_detail_logger(excel_path_or_name, product_id):
    """设置详情处理专用日志"""
    import logging
    from datetime import datetime

    # 创建详情处理专用的logger
    detail_logger_name = f"detail_processor_{product_id}"
    detail_logger = logging.getLogger(detail_logger_name)

    # 清除已有的handlers
    for handler in detail_logger.handlers[:]:
        detail_logger.removeHandler(handler)

    detail_logger.setLevel(logging.INFO)

    # 设置日志文件名
    if isinstance(excel_path_or_name, str) and excel_path_or_name.endswith('.xlsx'):
        base_name = os.path.splitext(os.path.basename(excel_path_or_name))[0]
    else:
        base_name = "详情处理"

    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_filename = f"{base_name}_详情处理_{timestamp}.log"
    log_path = os.path.join(LOG_DIR, log_filename)

    # 确保日志目录存在
    os.makedirs(LOG_DIR, exist_ok=True)

    # 文件处理器
    file_handler = logging.FileHandler(log_path, encoding='utf-8')
    file_handler.setLevel(logging.INFO)

    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)

    # 设置格式
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - [%(funcName)s:%(lineno)d] - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    detail_logger.addHandler(file_handler)
    detail_logger.addHandler(console_handler)

    return detail_logger

def extract_detail_data_from_response(detail_response, detail_logger):
    """从影刀详情接口响应中提取数据

    参数:
        detail_response: 影刀返回的详情接口响应
        detail_logger: 详情处理专用日志器

    返回:
        dict: 提取的详情数据
    """
    try:
        detail_logger.info(f"🔍 [详情解析] 开始解析详情响应数据")

        # 检查响应格式
        if not isinstance(detail_response, dict):
            detail_logger.error(f"❌ [详情解析] 响应数据不是字典格式: {type(detail_response)}")
            return {}

        # 获取body数据
        body = detail_response.get('body', {})
        if isinstance(body, str):
            try:
                import json
                body = json.loads(body)
                detail_logger.info(f"✅ [详情解析] JSON字符串解析成功")
            except:
                detail_logger.error(f"❌ [详情解析] JSON字符串解析失败")
                return {}

        # 获取data数据
        data = body.get('data', {})
        if not data:
            detail_logger.warning(f"⚠️  [详情解析] 未找到data字段")
            return {}

        # 获取cardList
        card_list = data.get('cardList', [])
        if not card_list:
            detail_logger.warning(f"⚠️  [详情解析] 未找到cardList")
            return {}

        detail_logger.info(f"📊 [详情解析] 找到 {len(card_list)} 个卡片")

        # 提取详情数据
        detail_data = {}

        for i, card in enumerate(card_list):
            card_type = card.get('cardType')
            card_data = card.get('cardData', {})

            detail_logger.info(f"   🔍 [详情解析] 处理卡片 {i+1}: cardType={card_type}")

            # 处理商品详情卡片 (cardType 100005)
            if card_type == 100005:
                # 提取基本信息
                item_id = card_data.get('itemId')
                if item_id:
                    detail_data['商品ID'] = item_id
                    detail_logger.info(f"      ✅ 商品ID: {item_id}")

                # 提取发布时间
                publish_time = card_data.get('clickParam', {}).get('args', {}).get('publishTime')
                if publish_time:
                    try:
                        import datetime
                        # 转换时间戳为日期
                        timestamp = int(publish_time) / 1000  # 毫秒转秒
                        publish_date = datetime.datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
                        detail_data['详情发布时间'] = publish_date
                        detail_logger.info(f"      ✅ 发布时间: {publish_date}")
                    except:
                        detail_logger.warning(f"      ⚠️  发布时间解析失败: {publish_time}")

                # 提取地区信息
                area = card_data.get('area')
                if area:
                    detail_data['详情地区'] = area
                    detail_logger.info(f"      ✅ 地区: {area}")

                # 提取用户信息
                user_info = card_data.get('user', {})
                if user_info:
                    user_nick = user_info.get('userNick')
                    if user_nick:
                        detail_data['详情卖家昵称'] = user_nick
                        detail_logger.info(f"      ✅ 卖家昵称: {user_nick}")

                # 提取fishTags中的标签信息
                fish_tags = card_data.get('fishTags', {})
                if fish_tags:
                    detail_logger.info(f"      🏷️  [详情解析] 解析fishTags标签")

                    # 解析各个区域的标签
                    for region, region_data in fish_tags.items():
                        tag_list = region_data.get('tagList', [])
                        detail_logger.info(f"         📍 区域 {region}: {len(tag_list)} 个标签")

                        for tag in tag_list:
                            tag_data = tag.get('data', {})
                            label_id = tag_data.get('labelId')
                            content = tag_data.get('content', '')

                            if label_id and content:
                                detail_logger.info(f"            🏷️  标签 {label_id}: {content}")

                                # 根据labelId解析不同类型的标签
                                if label_id == '9':  # 想要人数
                                    detail_data['详情想要人数'] = content
                                elif label_id == '13':  # 包邮
                                    detail_data['详情包邮'] = '是'
                                elif label_id == '41':  # 原价
                                    detail_data['详情原价'] = content
                                elif label_id == '468':  # 关注过的人
                                    detail_data['详情关注过'] = '是'
                                elif label_id == '919':  # 卖家信用
                                    detail_data['详情卖家信用'] = content
                                elif label_id == '824':  # 回头客
                                    detail_data['详情回头客'] = content
                                elif label_id == '598':  # 降价信息
                                    detail_data['详情降价信息'] = content
                                elif label_id == '1017':  # 可小刀
                                    detail_data['详情可小刀'] = '是'

        detail_logger.info(f"✅ [详情解析] 解析完成，提取到 {len(detail_data)} 个字段")
        return detail_data

    except Exception as e:
        detail_logger.error(f"💥 [详情解析] 解析详情数据失败: {e}")
        return {}

def update_excel_with_detail_safe(excel_path, product_id, detail_data, detail_logger):
    """使用临时文件安全更新Excel中的商品详情数据

    参数:
        excel_path: Excel文件路径
        product_id: 商品ID
        detail_data: 详情数据字典
        detail_logger: 详情处理专用日志器

    返回:
        bool: 更新是否成功
    """
    import tempfile
    import shutil
    import time

    try:
        detail_logger.info(f"📝 [Excel更新] 开始更新Excel文件: {os.path.basename(excel_path)}")
        detail_logger.info(f"🎯 [Excel更新] 目标商品ID: {product_id}")

        # 创建临时文件
        temp_dir = os.path.dirname(excel_path)
        temp_file = os.path.join(temp_dir, f"temp_detail_{os.path.basename(excel_path)}")

        # 检查文件是否被占用，最多重试5次
        max_retries = 5
        df = None

        for attempt in range(max_retries):
            try:
                # 读取Excel文件
                detail_logger.info(f"📖 [Excel更新] 尝试读取Excel文件 (第 {attempt + 1}/{max_retries} 次)")
                df = pd.read_excel(excel_path, engine='openpyxl')
                detail_logger.info(f"✅ [Excel更新] Excel文件读取成功，共 {len(df)} 行数据")
                break
            except PermissionError:
                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 2  # 递增等待时间
                    detail_logger.warning(f"⚠️  [Excel更新] 文件被占用，等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                else:
                    detail_logger.error(f"❌ [Excel更新] 文件被占用，无法读取: {excel_path}")
                    return False
            except Exception as e:
                detail_logger.error(f"❌ [Excel更新] 读取Excel文件失败: {e}")
                return False

        if df is None:
            detail_logger.error(f"❌ [Excel更新] 无法读取Excel文件")
            return False

        # 查找对应的商品行
        product_row_index = None
        for index, row in df.iterrows():
            if str(row['商品ID']) == str(product_id):
                product_row_index = index
                break

        if product_row_index is None:
            detail_logger.warning(f"⚠️  [Excel更新] 未找到商品ID为 {product_id} 的记录")
            return False

        detail_logger.info(f"📍 [Excel更新] 找到商品记录，行号: {product_row_index + 2}")  # +2因为Excel从1开始且有表头

        # 添加新列（如果不存在）并更新数据
        updated_fields = []
        for field, value in detail_data.items():
            if field not in df.columns:
                df[field] = ''  # 添加新列
                detail_logger.info(f"➕ [Excel更新] 添加新列: {field}")

            # 更新数据
            df.at[product_row_index, field] = value
            updated_fields.append(f"{field}={value}")
            detail_logger.info(f"   ✅ 更新字段 {field}: {value}")

        # 先保存到临时文件
        detail_logger.info(f"💾 [Excel更新] 保存到临时文件: {temp_file}")
        with pd.ExcelWriter(temp_file, engine='openpyxl', mode='w') as writer:
            df.to_excel(writer, index=False, sheet_name='商品数据')

            # 格式化工作表
            worksheet = writer.sheets['商品数据']

            # 设置列宽
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width

        detail_logger.info(f"✅ [Excel更新] 临时文件保存成功")

        # 尝试替换原文件
        for attempt in range(max_retries):
            try:
                # 备份原文件
                backup_file = excel_path + ".backup"
                if os.path.exists(backup_file):
                    os.remove(backup_file)
                shutil.copy2(excel_path, backup_file)
                detail_logger.info(f"📋 [Excel更新] 创建备份文件: {backup_file}")

                # 替换原文件
                shutil.move(temp_file, excel_path)
                detail_logger.info(f"🔄 [Excel更新] 替换原文件成功")

                # 删除备份文件
                if os.path.exists(backup_file):
                    os.remove(backup_file)
                    detail_logger.info(f"🗑️  [Excel更新] 删除备份文件")

                detail_logger.info(f"✅ [Excel更新] Excel文件更新成功，更新了 {len(updated_fields)} 个字段")
                return True

            except PermissionError:
                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 3  # 递增等待时间
                    detail_logger.warning(f"⚠️  [Excel更新] 文件被占用，等待 {wait_time} 秒后重试替换...")
                    time.sleep(wait_time)
                else:
                    detail_logger.error(f"❌ [Excel更新] 文件被占用，无法更新: {excel_path}")
                    # 清理临时文件
                    if os.path.exists(temp_file):
                        os.remove(temp_file)
                    return False
            except Exception as e:
                detail_logger.error(f"❌ [Excel更新] 替换文件失败: {e}")
                # 清理临时文件
                if os.path.exists(temp_file):
                    os.remove(temp_file)
                return False

        return False

    except Exception as e:
        detail_logger.error(f"💥 [Excel更新] 更新Excel文件失败: {e}")
        return False

# ==================== 影刀专用详情处理函数 ====================

def yingdao_process_detail(detail_response, excel_path_or_name, product_id):
    """影刀专用 - 处理单个商品详情数据

    参数:
        detail_response: 详情页响应数据（影刀接口返回格式）
        excel_path_or_name: Excel文件路径或文件名
        product_id: 商品ID

    返回:
        bool: 处理是否成功
    """
    try:
        # 设置详情处理专用日志
        detail_logger = setup_detail_logger(excel_path_or_name, product_id)

        # 查找Excel文件
        excel_path = find_excel_file(excel_path_or_name)
        if not excel_path:
            detail_logger.error(f"❌ [详情处理] 未找到Excel文件: {excel_path_or_name}")
            return False

        detail_logger.info(f"🔍 [详情处理] 开始处理商品详情: {product_id}")
        detail_logger.info(f"📄 [详情处理] Excel文件: {excel_path}")

        # 解析详情数据
        detail_data = extract_detail_data_from_response(detail_response, detail_logger)
        if not detail_data:
            detail_logger.warning(f"⚠️  [详情处理] 详情数据解析失败: {product_id}")
            return False

        detail_logger.info(f"📊 [详情处理] 解析到详情数据: {len(detail_data)} 个字段")

        # 使用临时文件机制更新Excel
        success = update_excel_with_detail_safe(excel_path, product_id, detail_data, detail_logger)

        if success:
            detail_logger.info(f"✅ [详情处理] 商品详情处理成功: {product_id}")
        else:
            detail_logger.error(f"❌ [详情处理] 商品详情处理失败: {product_id}")

        return success

    except Exception as e:
        if 'detail_logger' in locals():
            detail_logger.error(f"💥 [详情处理] 处理商品详情异常: {product_id}, 错误: {e}")
        else:
            logger.error(f"💥 [详情处理] 处理商品详情异常: {product_id}, 错误: {e}")
        return False

def yingdao_batch_process_details(detail_response_list, excel_path_or_name):
    """影刀专用 - 批量处理商品详情数据

    参数:
        detail_response_list: 详情页响应数据列表，每个元素包含 {'response': response_data, 'product_id': product_id}
        excel_path_or_name: Excel文件路径或文件名

    返回:
        dict: 处理结果统计
    """
    try:
        # 设置批量处理日志
        detail_logger = setup_detail_logger(excel_path_or_name, "batch")

        detail_logger.info(f"🚀 [批量详情处理] 开始批量处理详情数据")
        detail_logger.info(f"📊 [批量详情处理] 待处理数量: {len(detail_response_list)}")

        # 统计信息
        stats = {
            'total': len(detail_response_list),
            'success': 0,
            'failed': 0,
            'errors': []
        }

        # 逐个处理
        for i, item in enumerate(detail_response_list):
            detail_response = item.get('response')
            product_id = item.get('product_id')

            detail_logger.info(f"📦 [批量详情处理] 处理第 {i+1}/{len(detail_response_list)} 个商品: {product_id}")

            try:
                success = yingdao_process_detail(detail_response, excel_path_or_name, product_id)
                if success:
                    stats['success'] += 1
                    detail_logger.info(f"   ✅ 商品 {product_id} 处理成功")
                else:
                    stats['failed'] += 1
                    stats['errors'].append(f"商品 {product_id} 处理失败")
                    detail_logger.error(f"   ❌ 商品 {product_id} 处理失败")
            except Exception as e:
                stats['failed'] += 1
                error_msg = f"商品 {product_id} 处理异常: {e}"
                stats['errors'].append(error_msg)
                detail_logger.error(f"   💥 {error_msg}")

        # 输出统计结果
        detail_logger.info(f"📈 [批量详情处理] 处理完成统计:")
        detail_logger.info(f"   📊 总数量: {stats['total']}")
        detail_logger.info(f"   ✅ 成功: {stats['success']}")
        detail_logger.info(f"   ❌ 失败: {stats['failed']}")
        detail_logger.info(f"   📋 成功率: {stats['success']/stats['total']*100:.1f}%")

        return stats

    except Exception as e:
        if 'detail_logger' in locals():
            detail_logger.error(f"💥 [批量详情处理] 批量处理异常: {e}")
        else:
            logger.error(f"💥 [批量详情处理] 批量处理异常: {e}")
        return {'total': 0, 'success': 0, 'failed': 0, 'errors': [str(e)]}
