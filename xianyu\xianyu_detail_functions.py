#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
闲鱼详情页数据处理器 - 纯函数版本（影刀专用）
作者: AI Assistant
功能: 处理详情页数据并更新Excel文件中对应的商品信息
"""

import json
import re
import pandas as pd
import os
from datetime import datetime
from typing import List, Dict, Any, Optional
import logging
from openpyxl import load_workbook
from openpyxl.styles import Font, PatternFill, Alignment

# 固定输出路径配置
OUTPUT_BASE_DIR = r"D:\AppData\SelfSync\Code\Python\Tool\toolProject\xianyu\download"
EXCEL_DIR = os.path.join(OUTPUT_BASE_DIR, "excel")
LOG_DIR = os.path.join(OUTPUT_BASE_DIR, "logs")

def ensure_output_dirs():
    """确保输出目录存在"""
    os.makedirs(EXCEL_DIR, exist_ok=True)
    os.makedirs(LOG_DIR, exist_ok=True)

# 全局变量
logger = None

def setup_logger(log_file=None):
    """设置日志配置"""
    global logger
    logger = logging.getLogger('xianyu_detail')
    logger.setLevel(logging.INFO)
    
    # 清除现有的处理器
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # 创建格式器
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - [%(funcName)s:%(lineno)d] - %(message)s')
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 文件处理器
    if log_file:
        # 确保输出目录存在
        ensure_output_dirs()
        log_path = os.path.join(LOG_DIR, log_file)
        
        file_handler = logging.FileHandler(log_path, encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        logger.info(f"日志文件已设置: {log_path}")
    
    return logger

# 初始化日志
logger = setup_logger()

def extract_detail_data(detail_response):
    """从详情页响应中提取数据"""
    try:
        detail_info = {}
        
        # 解析响应数据
        if isinstance(detail_response, str):
            data = json.loads(detail_response)
        else:
            data = detail_response
        
        # 提取商品详情信息
        if 'data' in data and 'cardList' in data['data']:
            for card in data['data']['cardList']:
                if card.get('cardType') == 100005:  # 商品卡片类型
                    card_data = card.get('cardData', {})
                    
                    # 基本信息
                    detail_info['item_id'] = card_data.get('itemId', '')
                    detail_info['title'] = card_data.get('title', '')
                    detail_info['price'] = card_data.get('price', '')
                    detail_info['desc'] = card_data.get('desc', '')
                    detail_info['area'] = card_data.get('area', '')
                    
                    # 用户信息
                    user_info = card_data.get('user', {})
                    detail_info['seller_nick'] = user_info.get('userNick', '')
                    detail_info['seller_avatar'] = user_info.get('avatar', '')
                    
                    # 图片信息
                    image_info = card_data.get('image', {})
                    detail_info['image_url'] = image_info.get('url', '')
                    detail_info['image_width'] = image_info.get('widthSize', 0)
                    detail_info['image_height'] = image_info.get('heightSize', 0)
                    
                    # 标签信息
                    fish_tags = card_data.get('fishTags', {})
                    detail_info['tags'] = extract_tags_from_detail(fish_tags)
                    
                    # 点击参数中的额外信息
                    click_param = card_data.get('clickParam', {})
                    if 'args' in click_param:
                        args = click_param['args']
                        detail_info['want_num'] = args.get('wantNum', '')
                        detail_info['publish_time'] = args.get('publishTime', '')
                        
                    break
        
        logger.info(f"✅ 详情数据提取成功，商品ID: {detail_info.get('item_id', 'Unknown')}")
        return detail_info
        
    except Exception as e:
        logger.error(f"❌ 详情数据提取失败: {e}")
        return {}

def extract_tags_from_detail(fish_tags):
    """从详情页标签中提取信息"""
    tags_info = {}
    
    try:
        for region, region_data in fish_tags.items():
            if 'tagList' in region_data:
                for tag in region_data['tagList']:
                    tag_data = tag.get('data', {})
                    label_id = tag_data.get('labelId', '')
                    content = tag_data.get('content', '')
                    
                    if label_id and content:
                        # 根据labelId分类标签
                        if label_id == '9':  # 想要人数
                            tags_info['want_count'] = content
                        elif label_id == '919':  # 卖家信用
                            tags_info['seller_credit'] = content
                        elif label_id == '731':  # 24小时图标
                            tags_info['is_24h'] = True
                        elif label_id == '664':  # 无理由退货
                            tags_info['no_reason_return'] = True
                        elif label_id == '13':  # 包邮
                            tags_info['free_shipping'] = True
                        elif label_id == '41':  # 原价
                            tags_info['original_price'] = content
                        elif label_id == '468':  # 关注过的人
                            tags_info['followed_seller'] = True
                            
    except Exception as e:
        logger.error(f"标签提取失败: {e}")
    
    return tags_info

def find_excel_file(excel_path_or_name):
    """查找Excel文件的完整路径

    参数:
        excel_path_or_name: 可以是完整路径或文件名
    """
    # 如果是完整路径且文件存在，直接返回
    if os.path.isabs(excel_path_or_name) and os.path.exists(excel_path_or_name):
        return excel_path_or_name

    # 如果是文件名，在EXCEL_DIR中查找
    if not os.path.isabs(excel_path_or_name):
        excel_path = os.path.join(EXCEL_DIR, excel_path_or_name)
        if os.path.exists(excel_path):
            return excel_path

        # 如果直接路径不存在，尝试查找最新的文件
        if not os.path.exists(EXCEL_DIR):
            logger.error(f"Excel目录不存在: {EXCEL_DIR}")
            return None

        # 查找匹配的文件
        for file in os.listdir(EXCEL_DIR):
            if file.endswith('.xlsx') and excel_path_or_name.replace('.xlsx', '') in file:
                return os.path.join(EXCEL_DIR, file)

    logger.error(f"未找到Excel文件: {excel_path_or_name}")
    return None

def update_excel_with_detail(excel_path_or_name, product_id, detail_data):
    """根据商品ID更新Excel文件中的详情数据"""
    try:
        # 查找Excel文件
        excel_path = find_excel_file(excel_path_or_name)
        if not excel_path:
            return False
        
        logger.info(f"📄 开始更新Excel文件: {excel_path}")
        logger.info(f"🎯 目标商品ID: {product_id}")
        
        # 读取Excel文件
        df = pd.read_excel(excel_path, sheet_name='商品数据')
        logger.info(f"📊 Excel文件读取成功，共 {len(df)} 行数据")
        
        # 查找对应的商品行
        product_row_index = None
        for index, row in df.iterrows():
            if str(row['商品ID']) == str(product_id):
                product_row_index = index
                break
        
        if product_row_index is None:
            logger.warning(f"⚠️  未找到商品ID为 {product_id} 的记录")
            return False
        
        logger.info(f"✅ 找到商品记录，行索引: {product_row_index}")
        
        # 更新详情数据
        updated_fields = []
        
        # 更新卖家信息
        if 'seller_nick' in detail_data and detail_data['seller_nick']:
            if '卖家昵称' not in df.columns:
                df['卖家昵称'] = ''
            df.at[product_row_index, '卖家昵称'] = detail_data['seller_nick']
            updated_fields.append('卖家昵称')
        
        # 更新地区信息
        if 'area' in detail_data and detail_data['area']:
            if '地区' not in df.columns:
                df['地区'] = ''
            df.at[product_row_index, '地区'] = detail_data['area']
            updated_fields.append('地区')
        
        # 更新商品描述
        if 'desc' in detail_data and detail_data['desc']:
            if '商品描述' not in df.columns:
                df['商品描述'] = ''
            # 截取描述前200字符
            desc_short = detail_data['desc'][:200] + '...' if len(detail_data['desc']) > 200 else detail_data['desc']
            df.at[product_row_index, '商品描述'] = desc_short
            updated_fields.append('商品描述')
        
        # 更新标签信息
        tags = detail_data.get('tags', {})
        
        # 卖家信用
        if 'seller_credit' in tags:
            if '卖家信用' not in df.columns:
                df['卖家信用'] = ''
            df.at[product_row_index, '卖家信用'] = tags['seller_credit']
            updated_fields.append('卖家信用')
        
        # 服务标签
        service_tags = []
        if tags.get('is_24h'):
            service_tags.append('24小时发货')
        if tags.get('no_reason_return'):
            service_tags.append('无理由退货')
        if tags.get('free_shipping'):
            service_tags.append('包邮')
        if tags.get('followed_seller'):
            service_tags.append('关注过的卖家')
        
        if service_tags:
            if '服务标签' not in df.columns:
                df['服务标签'] = ''
            df.at[product_row_index, '服务标签'] = '; '.join(service_tags)
            updated_fields.append('服务标签')
        
        # 原价信息
        if 'original_price' in tags:
            if '原价' not in df.columns:
                df['原价'] = ''
            df.at[product_row_index, '原价'] = tags['original_price']
            updated_fields.append('原价')
        
        # 更新详情获取状态
        if '详情已获取' not in df.columns:
            df['详情已获取'] = '否'
        df.at[product_row_index, '详情已获取'] = '是'
        updated_fields.append('详情已获取')
        
        # 更新时间
        if '详情更新时间' not in df.columns:
            df['详情更新时间'] = ''
        df.at[product_row_index, '详情更新时间'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        updated_fields.append('详情更新时间')
        
        logger.info(f"📝 更新字段: {', '.join(updated_fields)}")
        
        # 保存更新后的Excel文件
        with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='商品数据', index=False)
        
        # 重新格式化Excel文件
        format_excel_file(excel_path)
        
        logger.info(f"✅ Excel文件更新成功: {excel_path}")
        return True
        
    except Exception as e:
        logger.error(f"❌ Excel文件更新失败: {e}")
        return False

def format_excel_file(excel_path):
    """格式化Excel文件"""
    try:
        wb = load_workbook(excel_path)
        ws = wb.active
        
        # 设置标题行样式
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        
        for cell in ws[1]:
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = Alignment(horizontal="center", vertical="center")
        
        # 自动调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width
        
        # 添加筛选器
        ws.auto_filter.ref = ws.dimensions
        
        wb.save(excel_path)
        logger.info(f"🎨 Excel文件格式化完成")

    except Exception as e:
        logger.error(f"Excel格式化失败: {e}")

# ==================== 影刀专用函数 ====================

def yingdao_process_detail(detail_response_json, excel_path_or_name, product_id):
    """
    影刀专用：处理详情页数据并更新Excel

    参数:
        detail_response_json: 详情页接口返回的JSON字符串
        excel_path_or_name: Excel文件的完整路径或文件名
        product_id: 商品ID

    返回:
        dict: 处理结果
    """
    try:
        # 设置日志文件
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        log_file = f"详情页处理_{timestamp}.log"
        logger = setup_logger(log_file)

        logger.info("=" * 80)
        logger.info("🚀 开始处理详情页数据")
        logger.info(f"📄 Excel文件: {excel_filename}")
        logger.info(f"🎯 商品ID: {product_id}")
        logger.info("=" * 80)

        # 提取详情数据
        detail_data = extract_detail_data(detail_response_json)
        if not detail_data:
            logger.error("❌ 详情数据提取失败")
            return {
                'success': False,
                'message': '详情数据提取失败',
                'product_id': product_id
            }

        # 更新Excel文件
        update_success = update_excel_with_detail(excel_path_or_name, product_id, detail_data)

        if update_success:
            logger.info("✅ 详情页数据处理完成")
            return {
                'success': True,
                'message': '详情页数据更新成功',
                'product_id': product_id,
                'updated_fields': ['卖家昵称', '地区', '商品描述', '卖家信用', '服务标签', '原价', '详情已获取', '详情更新时间']
            }
        else:
            logger.error("❌ Excel文件更新失败")
            return {
                'success': False,
                'message': 'Excel文件更新失败',
                'product_id': product_id
            }

    except Exception as e:
        logger.error(f"❌ 详情页数据处理异常: {e}")
        return {
            'success': False,
            'message': f'处理异常: {str(e)}',
            'product_id': product_id
        }

def yingdao_batch_process_details(detail_list, excel_path_or_name):
    """
    影刀专用：批量处理详情页数据

    参数:
        detail_list: 详情数据列表，每个元素包含 {'product_id': '', 'detail_response': ''}
        excel_path_or_name: Excel文件的完整路径或文件名

    返回:
        dict: 批量处理结果
    """
    try:
        # 设置日志文件
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        log_file = f"批量详情处理_{timestamp}.log"
        logger = setup_logger(log_file)

        logger.info("=" * 80)
        logger.info("🚀 开始批量处理详情页数据")
        logger.info(f"📄 Excel文件: {excel_path_or_name}")
        logger.info(f"📊 待处理数量: {len(detail_list)}")
        logger.info("=" * 80)

        results = {
            'success_count': 0,
            'failed_count': 0,
            'total_count': len(detail_list),
            'details': []
        }

        for i, detail_item in enumerate(detail_list, 1):
            product_id = detail_item.get('product_id', '')
            detail_response = detail_item.get('detail_response', '')

            logger.info(f"📝 处理第 {i}/{len(detail_list)} 个商品: {product_id}")

            # 处理单个详情
            result = yingdao_process_detail(detail_response, excel_path_or_name, product_id)
            results['details'].append(result)

            if result['success']:
                results['success_count'] += 1
                logger.info(f"✅ 商品 {product_id} 处理成功")
            else:
                results['failed_count'] += 1
                logger.error(f"❌ 商品 {product_id} 处理失败: {result['message']}")

        logger.info("=" * 80)
        logger.info(f"📊 批量处理完成")
        logger.info(f"   ✅ 成功: {results['success_count']}")
        logger.info(f"   ❌ 失败: {results['failed_count']}")
        logger.info(f"   📊 总计: {results['total_count']}")
        logger.info("=" * 80)

        return results

    except Exception as e:
        logger.error(f"❌ 批量处理异常: {e}")
        return {
            'success_count': 0,
            'failed_count': len(detail_list) if detail_list else 0,
            'total_count': len(detail_list) if detail_list else 0,
            'error': str(e),
            'details': []
        }

def yingdao_get_pending_products(excel_path_or_name):
    """
    影刀专用：获取需要处理详情的商品列表

    参数:
        excel_path_or_name: Excel文件的完整路径或文件名

    返回:
        list: 需要处理详情的商品列表
    """
    try:
        # 查找Excel文件
        excel_path = find_excel_file(excel_path_or_name)
        if not excel_path:
            return []

        # 读取Excel文件
        df = pd.read_excel(excel_path, sheet_name='商品数据')

        # 查找需要详情的商品
        pending_products = []

        for index, row in df.iterrows():
            # 检查是否标记为需要详情
            need_details = row.get('需要详情', '否') == '是'
            # 检查是否已获取详情
            has_details = row.get('详情已获取', '否') == '是'

            if need_details and not has_details:
                pending_products.append({
                    'product_id': str(row['商品ID']),
                    'title': row.get('商品标题', ''),
                    'price': row.get('价格', ''),
                    'detail_url': row.get('详情链接', '')
                })

        logger.info(f"📊 找到 {len(pending_products)} 个待处理详情的商品")
        return pending_products

    except Exception as e:
        logger.error(f"❌ 获取待处理商品失败: {e}")
        return []

# ==================== 新版详情处理函数 ====================

def setup_detail_logger(excel_path_or_name, product_id):
    """设置详情处理专用日志"""
    import logging
    from datetime import datetime

    # 创建详情处理专用的logger
    detail_logger_name = f"detail_processor_{product_id}"
    detail_logger = logging.getLogger(detail_logger_name)

    # 清除已有的handlers
    for handler in detail_logger.handlers[:]:
        detail_logger.removeHandler(handler)

    detail_logger.setLevel(logging.INFO)

    # 设置日志文件名
    if isinstance(excel_path_or_name, str) and excel_path_or_name.endswith('.xlsx'):
        base_name = os.path.splitext(os.path.basename(excel_path_or_name))[0]
    else:
        base_name = "详情处理"

    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_filename = f"{base_name}_详情处理_{timestamp}.log"
    log_path = os.path.join(LOG_DIR, log_filename)

    # 确保日志目录存在
    os.makedirs(LOG_DIR, exist_ok=True)

    # 文件处理器
    file_handler = logging.FileHandler(log_path, encoding='utf-8')
    file_handler.setLevel(logging.INFO)

    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)

    # 设置格式
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - [%(funcName)s:%(lineno)d] - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    detail_logger.addHandler(file_handler)
    detail_logger.addHandler(console_handler)

    return detail_logger

def extract_detail_data_from_response(detail_response, detail_logger):
    """从影刀详情接口响应中提取数据

    参数:
        detail_response: 影刀返回的详情接口响应
        detail_logger: 详情处理专用日志器

    返回:
        dict: 提取的详情数据
    """
    try:
        detail_logger.info(f"🔍 [详情解析] 开始解析详情响应数据")

        # 检查响应格式
        if not isinstance(detail_response, dict):
            detail_logger.error(f"❌ [详情解析] 响应数据不是字典格式: {type(detail_response)}")
            return {}

        # 获取body数据
        body = detail_response.get('body', {})
        if isinstance(body, str):
            try:
                import json
                body = json.loads(body)
                detail_logger.info(f"✅ [详情解析] JSON字符串解析成功")
            except:
                detail_logger.error(f"❌ [详情解析] JSON字符串解析失败")
                return {}

        # 获取data数据
        data = body.get('data', {})
        if not data:
            detail_logger.warning(f"⚠️  [详情解析] 未找到data字段")
            return {}

        # 获取cardList
        card_list = data.get('cardList', [])
        if not card_list:
            detail_logger.warning(f"⚠️  [详情解析] 未找到cardList")
            return {}

        detail_logger.info(f"📊 [详情解析] 找到 {len(card_list)} 个卡片")

        # 提取详情数据
        detail_data = {}

        for i, card in enumerate(card_list):
            card_type = card.get('cardType')
            card_data = card.get('cardData', {})

            detail_logger.info(f"   🔍 [详情解析] 处理卡片 {i+1}: cardType={card_type}")

            # 处理商品详情卡片 (cardType 100005)
            if card_type == 100005:
                # 提取基本信息
                item_id = card_data.get('itemId')
                if item_id:
                    detail_data['商品ID'] = item_id
                    detail_logger.info(f"      ✅ 商品ID: {item_id}")

                # 提取发布时间
                publish_time = card_data.get('clickParam', {}).get('args', {}).get('publishTime')
                if publish_time:
                    try:
                        import datetime
                        # 转换时间戳为日期
                        timestamp = int(publish_time) / 1000  # 毫秒转秒
                        publish_date = datetime.datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
                        detail_data['详情发布时间'] = publish_date
                        detail_logger.info(f"      ✅ 发布时间: {publish_date}")
                    except:
                        detail_logger.warning(f"      ⚠️  发布时间解析失败: {publish_time}")

                # 提取地区信息
                area = card_data.get('area')
                if area:
                    detail_data['详情地区'] = area
                    detail_logger.info(f"      ✅ 地区: {area}")

                # 提取用户信息
                user_info = card_data.get('user', {})
                if user_info:
                    user_nick = user_info.get('userNick')
                    if user_nick:
                        detail_data['详情卖家昵称'] = user_nick
                        detail_logger.info(f"      ✅ 卖家昵称: {user_nick}")

                # 提取fishTags中的标签信息
                fish_tags = card_data.get('fishTags', {})
                if fish_tags:
                    detail_logger.info(f"      🏷️  [详情解析] 解析fishTags标签")

                    # 解析各个区域的标签
                    for region, region_data in fish_tags.items():
                        tag_list = region_data.get('tagList', [])
                        detail_logger.info(f"         📍 区域 {region}: {len(tag_list)} 个标签")

                        for tag in tag_list:
                            tag_data = tag.get('data', {})
                            label_id = tag_data.get('labelId')
                            content = tag_data.get('content', '')

                            if label_id and content:
                                detail_logger.info(f"            🏷️  标签 {label_id}: {content}")

                                # 根据labelId解析不同类型的标签
                                if label_id == '9':  # 想要人数
                                    detail_data['详情想要人数'] = content
                                elif label_id == '13':  # 包邮
                                    detail_data['详情包邮'] = '是'
                                elif label_id == '41':  # 原价
                                    detail_data['详情原价'] = content
                                elif label_id == '468':  # 关注过的人
                                    detail_data['详情关注过'] = '是'
                                elif label_id == '919':  # 卖家信用
                                    detail_data['详情卖家信用'] = content
                                elif label_id == '824':  # 回头客
                                    detail_data['详情回头客'] = content
                                elif label_id == '598':  # 降价信息
                                    detail_data['详情降价信息'] = content
                                elif label_id == '1017':  # 可小刀
                                    detail_data['详情可小刀'] = '是'

        detail_logger.info(f"✅ [详情解析] 解析完成，提取到 {len(detail_data)} 个字段")
        return detail_data

    except Exception as e:
        detail_logger.error(f"💥 [详情解析] 解析详情数据失败: {e}")
        return {}

def update_excel_with_detail_safe(excel_path, product_id, detail_data, detail_logger):
    """使用临时文件安全更新Excel中的商品详情数据

    参数:
        excel_path: Excel文件路径
        product_id: 商品ID
        detail_data: 详情数据字典
        detail_logger: 详情处理专用日志器

    返回:
        bool: 更新是否成功
    """
    import tempfile
    import shutil
    import time

    try:
        detail_logger.info(f"📝 [Excel更新] 开始更新Excel文件: {os.path.basename(excel_path)}")
        detail_logger.info(f"🎯 [Excel更新] 目标商品ID: {product_id}")

        # 创建临时文件
        temp_dir = os.path.dirname(excel_path)
        temp_file = os.path.join(temp_dir, f"temp_detail_{os.path.basename(excel_path)}")

        # 检查文件是否被占用，最多重试5次
        max_retries = 5
        df = None

        for attempt in range(max_retries):
            try:
                # 读取Excel文件
                detail_logger.info(f"📖 [Excel更新] 尝试读取Excel文件 (第 {attempt + 1}/{max_retries} 次)")
                df = pd.read_excel(excel_path, engine='openpyxl')
                detail_logger.info(f"✅ [Excel更新] Excel文件读取成功，共 {len(df)} 行数据")
                break
            except PermissionError:
                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 2  # 递增等待时间
                    detail_logger.warning(f"⚠️  [Excel更新] 文件被占用，等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                else:
                    detail_logger.error(f"❌ [Excel更新] 文件被占用，无法读取: {excel_path}")
                    return False
            except Exception as e:
                detail_logger.error(f"❌ [Excel更新] 读取Excel文件失败: {e}")
                return False

        if df is None:
            detail_logger.error(f"❌ [Excel更新] 无法读取Excel文件")
            return False

        # 查找对应的商品行
        product_row_index = None
        for index, row in df.iterrows():
            if str(row['商品ID']) == str(product_id):
                product_row_index = index
                break

        if product_row_index is None:
            detail_logger.warning(f"⚠️  [Excel更新] 未找到商品ID为 {product_id} 的记录")
            return False

        detail_logger.info(f"📍 [Excel更新] 找到商品记录，行号: {product_row_index + 2}")  # +2因为Excel从1开始且有表头

        # 添加新列（如果不存在）并更新数据
        updated_fields = []
        for field, value in detail_data.items():
            if field not in df.columns:
                df[field] = ''  # 添加新列
                detail_logger.info(f"➕ [Excel更新] 添加新列: {field}")

            # 更新数据
            df.at[product_row_index, field] = value
            updated_fields.append(f"{field}={value}")
            detail_logger.info(f"   ✅ 更新字段 {field}: {value}")

        # 先保存到临时文件
        detail_logger.info(f"💾 [Excel更新] 保存到临时文件: {temp_file}")
        with pd.ExcelWriter(temp_file, engine='openpyxl', mode='w') as writer:
            df.to_excel(writer, index=False, sheet_name='商品数据')

            # 格式化工作表
            worksheet = writer.sheets['商品数据']

            # 设置列宽
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width

        detail_logger.info(f"✅ [Excel更新] 临时文件保存成功")

        # 尝试替换原文件
        for attempt in range(max_retries):
            try:
                # 备份原文件
                backup_file = excel_path + ".backup"
                if os.path.exists(backup_file):
                    os.remove(backup_file)
                shutil.copy2(excel_path, backup_file)
                detail_logger.info(f"📋 [Excel更新] 创建备份文件: {backup_file}")

                # 替换原文件
                shutil.move(temp_file, excel_path)
                detail_logger.info(f"🔄 [Excel更新] 替换原文件成功")

                # 删除备份文件
                if os.path.exists(backup_file):
                    os.remove(backup_file)
                    detail_logger.info(f"🗑️  [Excel更新] 删除备份文件")

                detail_logger.info(f"✅ [Excel更新] Excel文件更新成功，更新了 {len(updated_fields)} 个字段")
                return True

            except PermissionError:
                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 3  # 递增等待时间
                    detail_logger.warning(f"⚠️  [Excel更新] 文件被占用，等待 {wait_time} 秒后重试替换...")
                    time.sleep(wait_time)
                else:
                    detail_logger.error(f"❌ [Excel更新] 文件被占用，无法更新: {excel_path}")
                    # 清理临时文件
                    if os.path.exists(temp_file):
                        os.remove(temp_file)
                    return False
            except Exception as e:
                detail_logger.error(f"❌ [Excel更新] 替换文件失败: {e}")
                # 清理临时文件
                if os.path.exists(temp_file):
                    os.remove(temp_file)
                return False

        return False

    except Exception as e:
        detail_logger.error(f"💥 [Excel更新] 更新Excel文件失败: {e}")
        return False

# ==================== 影刀专用详情处理函数 ====================

def yingdao_process_detail(detail_response, excel_path_or_name, product_id):
    """影刀专用 - 处理单个商品详情数据

    参数:
        detail_response: 详情页响应数据（影刀接口返回格式）
        excel_path_or_name: Excel文件路径或文件名
        product_id: 商品ID

    返回:
        bool: 处理是否成功
    """
    try:
        # 设置详情处理专用日志
        detail_logger = setup_detail_logger(excel_path_or_name, product_id)

        # 查找Excel文件
        excel_path = find_excel_file(excel_path_or_name)
        if not excel_path:
            detail_logger.error(f"❌ [详情处理] 未找到Excel文件: {excel_path_or_name}")
            return False

        detail_logger.info(f"🔍 [详情处理] 开始处理商品详情: {product_id}")
        detail_logger.info(f"📄 [详情处理] Excel文件: {excel_path}")

        # 解析详情数据
        detail_data = extract_detail_data_from_response(detail_response, detail_logger)
        if not detail_data:
            detail_logger.warning(f"⚠️  [详情处理] 详情数据解析失败: {product_id}")
            return False

        detail_logger.info(f"📊 [详情处理] 解析到详情数据: {len(detail_data)} 个字段")

        # 使用临时文件机制更新Excel
        success = update_excel_with_detail_safe(excel_path, product_id, detail_data, detail_logger)

        if success:
            detail_logger.info(f"✅ [详情处理] 商品详情处理成功: {product_id}")
        else:
            detail_logger.error(f"❌ [详情处理] 商品详情处理失败: {product_id}")

        return success

    except Exception as e:
        if 'detail_logger' in locals():
            detail_logger.error(f"💥 [详情处理] 处理商品详情异常: {product_id}, 错误: {e}")
        else:
            logger.error(f"💥 [详情处理] 处理商品详情异常: {product_id}, 错误: {e}")
        return False

def yingdao_batch_process_details(detail_response_list, excel_path_or_name):
    """影刀专用 - 批量处理商品详情数据

    参数:
        detail_response_list: 详情页响应数据列表，每个元素包含 {'response': response_data, 'product_id': product_id}
        excel_path_or_name: Excel文件路径或文件名

    返回:
        dict: 处理结果统计
    """
    try:
        # 设置批量处理日志
        detail_logger = setup_detail_logger(excel_path_or_name, "batch")

        detail_logger.info(f"🚀 [批量详情处理] 开始批量处理详情数据")
        detail_logger.info(f"📊 [批量详情处理] 待处理数量: {len(detail_response_list)}")

        # 统计信息
        stats = {
            'total': len(detail_response_list),
            'success': 0,
            'failed': 0,
            'errors': []
        }

        # 逐个处理
        for i, item in enumerate(detail_response_list):
            detail_response = item.get('response')
            product_id = item.get('product_id')

            detail_logger.info(f"📦 [批量详情处理] 处理第 {i+1}/{len(detail_response_list)} 个商品: {product_id}")

            try:
                success = yingdao_process_detail(detail_response, excel_path_or_name, product_id)
                if success:
                    stats['success'] += 1
                    detail_logger.info(f"   ✅ 商品 {product_id} 处理成功")
                else:
                    stats['failed'] += 1
                    stats['errors'].append(f"商品 {product_id} 处理失败")
                    detail_logger.error(f"   ❌ 商品 {product_id} 处理失败")
            except Exception as e:
                stats['failed'] += 1
                error_msg = f"商品 {product_id} 处理异常: {e}"
                stats['errors'].append(error_msg)
                detail_logger.error(f"   💥 {error_msg}")

        # 输出统计结果
        detail_logger.info(f"📈 [批量详情处理] 处理完成统计:")
        detail_logger.info(f"   📊 总数量: {stats['total']}")
        detail_logger.info(f"   ✅ 成功: {stats['success']}")
        detail_logger.info(f"   ❌ 失败: {stats['failed']}")
        detail_logger.info(f"   📋 成功率: {stats['success']/stats['total']*100:.1f}%")

        return stats

    except Exception as e:
        if 'detail_logger' in locals():
            detail_logger.error(f"💥 [批量详情处理] 批量处理异常: {e}")
        else:
            logger.error(f"💥 [批量详情处理] 批量处理异常: {e}")
        return {'total': 0, 'success': 0, 'failed': 0, 'errors': [str(e)]}
